'use client';

import { useState } from 'react';

interface QuadrantSelectorProps {
  importance: number;
  urgency: number;
  onChange: (importance: number, urgency: number) => void;
}

export default function QuadrantSelector({ importance, urgency, onChange }: QuadrantSelectorProps) {
  const [selectedQuadrant, setSelectedQuadrant] = useState<number | null>(null);

  // 根据重要性和紧急性确定象限
  const getQuadrant = (imp: number, urg: number): number => {
    if (imp >= 4 && urg >= 4) return 1; // 重要且紧急
    if (imp >= 4 && urg < 4) return 2;  // 重要不紧急
    if (imp < 4 && urg >= 4) return 3;  // 不重要但紧急
    return 4; // 不重要不紧急
  };

  const currentQuadrant = getQuadrant(importance, urgency);

  // 象限配置
  const quadrants = [
    {
      id: 1,
      title: '重要且紧急',
      description: '立即处理',
      color: 'bg-red-100 border-red-300 hover:bg-red-200',
      selectedColor: 'bg-red-200 border-red-500',
      textColor: 'text-red-800',
      importance: 5,
      urgency: 5,
      position: 'top-right'
    },
    {
      id: 2,
      title: '重要不紧急',
      description: '计划安排',
      color: 'bg-blue-100 border-blue-300 hover:bg-blue-200',
      selectedColor: 'bg-blue-200 border-blue-500',
      textColor: 'text-blue-800',
      importance: 5,
      urgency: 2,
      position: 'top-left'
    },
    {
      id: 3,
      title: '不重要但紧急',
      description: '委托处理',
      color: 'bg-yellow-100 border-yellow-300 hover:bg-yellow-200',
      selectedColor: 'bg-yellow-200 border-yellow-500',
      textColor: 'text-yellow-800',
      importance: 2,
      urgency: 5,
      position: 'bottom-right'
    },
    {
      id: 4,
      title: '不重要不紧急',
      description: '减少或删除',
      color: 'bg-gray-100 border-gray-300 hover:bg-gray-200',
      selectedColor: 'bg-gray-200 border-gray-500',
      textColor: 'text-gray-800',
      importance: 2,
      urgency: 2,
      position: 'bottom-left'
    }
  ];

  const handleQuadrantClick = (quadrant: typeof quadrants[0]) => {
    setSelectedQuadrant(quadrant.id);
    onChange(quadrant.importance, quadrant.urgency);
  };

  return (
    <div className="space-y-4">
      <div className="text-sm font-medium text-gray-700 mb-2">
        任务优先级（点击象限选择）
      </div>
      
      {/* 四象限网格 */}
      <div className="relative w-full max-w-md mx-auto">
        {/* 坐标轴标签 */}
        <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 font-medium">
          紧急程度 →
        </div>
        <div className="absolute -left-12 top-1/2 transform -translate-y-1/2 -rotate-90 text-xs text-gray-500 font-medium">
          重要程度 →
        </div>
        
        {/* 象限网格 */}
        <div className="grid grid-cols-2 gap-2 w-80 h-80">
          {/* 第二象限：重要不紧急 */}
          <div
            className={`
              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
              flex flex-col justify-center items-center text-center
              ${currentQuadrant === 2 ? quadrants[1].selectedColor : quadrants[1].color}
              ${quadrants[1].textColor}
            `}
            onClick={() => handleQuadrantClick(quadrants[1])}
          >
            <div className="font-semibold text-sm mb-1">重要</div>
            <div className="font-semibold text-sm mb-2">不紧急</div>
            <div className="text-xs opacity-75">计划安排</div>
            <div className="text-xs mt-1 font-bold">象限 II</div>
          </div>

          {/* 第一象限：重要且紧急 */}
          <div
            className={`
              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
              flex flex-col justify-center items-center text-center
              ${currentQuadrant === 1 ? quadrants[0].selectedColor : quadrants[0].color}
              ${quadrants[0].textColor}
            `}
            onClick={() => handleQuadrantClick(quadrants[0])}
          >
            <div className="font-semibold text-sm mb-1">重要</div>
            <div className="font-semibold text-sm mb-2">紧急</div>
            <div className="text-xs opacity-75">立即处理</div>
            <div className="text-xs mt-1 font-bold">象限 I</div>
          </div>

          {/* 第四象限：不重要不紧急 */}
          <div
            className={`
              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
              flex flex-col justify-center items-center text-center
              ${currentQuadrant === 4 ? quadrants[3].selectedColor : quadrants[3].color}
              ${quadrants[3].textColor}
            `}
            onClick={() => handleQuadrantClick(quadrants[3])}
          >
            <div className="font-semibold text-sm mb-1">不重要</div>
            <div className="font-semibold text-sm mb-2">不紧急</div>
            <div className="text-xs opacity-75">减少删除</div>
            <div className="text-xs mt-1 font-bold">象限 IV</div>
          </div>

          {/* 第三象限：不重要但紧急 */}
          <div
            className={`
              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
              flex flex-col justify-center items-center text-center
              ${currentQuadrant === 3 ? quadrants[2].selectedColor : quadrants[2].color}
              ${quadrants[2].textColor}
            `}
            onClick={() => handleQuadrantClick(quadrants[2])}
          >
            <div className="font-semibold text-sm mb-1">不重要</div>
            <div className="font-semibold text-sm mb-2">紧急</div>
            <div className="text-xs opacity-75">委托处理</div>
            <div className="text-xs mt-1 font-bold">象限 III</div>
          </div>
        </div>

        {/* 当前选择显示 */}
        <div className="mt-4 text-center">
          <div className="text-sm text-gray-600">
            当前选择：<span className="font-medium">象限 {currentQuadrant}</span>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            重要性: {importance}/5 | 紧急性: {urgency}/5
          </div>
        </div>
      </div>
    </div>
  );
}
