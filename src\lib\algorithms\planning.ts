import { Task, ScoredTask, DailySchedule, TimeSlot } from '@/types';

export class PlanningAlgorithm {
  /**
   * 计算任务的综合分数
   */
  calculateTaskScore(task: Task): number {
    // 基础分数：重要性权重0.6，紧急性权重0.4
    const baseScore = task.importance * 0.6 + task.urgency * 0.4;
    
    // 分类加权
    const categoryBonus = {
      work: 0,
      improvement: 2,
      entertainment: 1
    }[task.category];
    
    // 推迟惩罚：每推迟一次扣3分
    const postponePenalty = task.postponeCount * 3;
    
    // 截止时间紧迫性加权
    const deadlineBonus = this.calculateDeadlineUrgency(task.deadline);
    
    return baseScore + categoryBonus + postponePenalty + deadlineBonus;
  }
  
  /**
   * 根据截止时间计算紧迫性加权
   */
  private calculateDeadlineUrgency(deadline: Date): number {
    const now = new Date();
    const hoursUntilDeadline = (deadline.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (hoursUntilDeadline < 0) return 10; // 已过期，最高优先级
    if (hoursUntilDeadline < 2) return 8;  // 2小时内
    if (hoursUntilDeadline < 6) return 6;  // 6小时内
    if (hoursUntilDeadline < 24) return 4; // 24小时内
    if (hoursUntilDeadline < 72) return 2; // 3天内
    return 0; // 3天以上
  }
  
  /**
   * 根据重要性和紧急性确定四象限
   */
  classifyQuadrant(importance: number, urgency: number): 1 | 2 | 3 | 4 {
    const isImportant = importance >= 4;
    const isUrgent = urgency >= 4;
    
    if (isImportant && isUrgent) return 1;      // 重要且紧急
    if (isImportant && !isUrgent) return 2;    // 重要不紧急
    if (!isImportant && isUrgent) return 3;    // 不重要但紧急
    return 4;                                   // 不重要不紧急
  }
  
  /**
   * 生成今日时间安排
   */
  generateDailySchedule(tasks: Task[], workHours = { start: '09:00', end: '18:00' }): DailySchedule {
    // 1. 过滤今日需要处理的任务
    const todayTasks = this.filterTodayTasks(tasks);
    
    // 2. 计算分数并分类
    const scoredTasks: ScoredTask[] = todayTasks
      .map(task => ({
        ...task,
        score: this.calculateTaskScore(task),
        quadrant: this.classifyQuadrant(task.importance, task.urgency)
      }))
      .sort((a, b) => {
        // 先按象限排序，再按分数排序
        if (a.quadrant !== b.quadrant) {
          return a.quadrant - b.quadrant;
        }
        return b.score - a.score;
      });
    
    // 3. 生成时间段
    const timeSlots = this.generateTimeSlots(scoredTasks, workHours);
    
    // 4. 计算总时长
    const totalDuration = timeSlots.reduce((sum, slot) => 
      sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0
    );
    
    return {
      date: new Date(),
      timeSlots,
      totalTasks: todayTasks.length,
      estimatedDuration: totalDuration
    };
  }
  
  /**
   * 过滤今日需要处理的任务
   */
  private filterTodayTasks(tasks: Task[]): Task[] {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    return tasks.filter(task => {
      // 包含今日截止的任务和未完成的高优先级任务
      const isToday = task.deadline <= tomorrow;
      const isHighPriority = task.importance >= 4 || task.urgency >= 4;
      const isPending = task.status === 'pending' || task.status === 'in-progress';
      
      return isPending && (isToday || isHighPriority);
    });
  }
  
  /**
   * 生成时间段安排
   */
  private generateTimeSlots(tasks: ScoredTask[], workHours: { start: string; end: string }): TimeSlot[] {
    const timeSlots: TimeSlot[] = [];
    const today = new Date();
    
    // 解析工作时间
    const [startHour, startMinute] = workHours.start.split(':').map(Number);
    const [endHour, endMinute] = workHours.end.split(':').map(Number);
    
    let currentTime = new Date(today);
    currentTime.setHours(startHour, startMinute, 0, 0);
    
    const workEndTime = new Date(today);
    workEndTime.setHours(endHour, endMinute, 0, 0);
    
    for (const task of tasks) {
      // 检查是否还有足够的工作时间
      const remainingWorkTime = workEndTime.getTime() - currentTime.getTime();
      const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒
      
      if (remainingWorkTime < taskDuration) {
        // 如果当天时间不够，跳过或安排到明天
        continue;
      }
      
      const endTime = new Date(currentTime.getTime() + taskDuration);
      
      timeSlots.push({
        task,
        startTime: new Date(currentTime),
        endTime,
        isFixed: false
      });
      
      // 更新当前时间，添加15分钟休息时间
      currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);
      
      // 如果超过工作时间，停止安排
      if (currentTime >= workEndTime) {
        break;
      }
    }
    
    return timeSlots;
  }
  
  /**
   * 获取四象限的描述
   */
  getQuadrantDescription(quadrant: 1 | 2 | 3 | 4): string {
    const descriptions = {
      1: '重要且紧急 - 立即执行',
      2: '重要不紧急 - 计划执行',
      3: '不重要但紧急 - 委托处理',
      4: '不重要不紧急 - 减少或删除'
    };
    return descriptions[quadrant];
  }
  
  /**
   * 获取任务建议
   */
  getTaskRecommendation(task: ScoredTask): string {
    if (task.quadrant === 1) {
      return '🔥 高优先级任务，建议立即处理';
    } else if (task.quadrant === 2) {
      return '📅 重要任务，建议合理安排时间';
    } else if (task.quadrant === 3) {
      return '⚡ 紧急但不重要，考虑委托或快速处理';
    } else {
      return '🤔 优先级较低，可以延后或删除';
    }
  }
}
