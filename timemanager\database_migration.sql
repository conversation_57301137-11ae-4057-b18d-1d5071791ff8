-- TimeManager 数据库迁移脚本
-- 添加用户时间配置和引导完成状态

-- 1. 为 user_profiles 表添加新字段
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS time_config JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT FALSE;

-- 2. 更新 work_hours 字段结构（添加工作日支持）
-- 注意：这会保留现有数据，只是扩展结构

-- 3. 创建时间调整历史表
CREATE TABLE IF NOT EXISTS time_adjustments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  adjustment_date DATE NOT NULL,
  trigger_type VARCHAR NOT NULL CHECK (trigger_type IN ('task_overrun', 'urgent_task', 'manual')),
  trigger_task_id UUID REFERENCES tasks(id) ON DELETE SET NULL,
  strategy_used VARCHAR NOT NULL,
  adjustments_made JSONB NOT NULL,
  impact_score INTEGER,
  user_accepted BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 4. 为时间调整表启用行级安全
ALTER TABLE time_adjustments ENABLE ROW LEVEL SECURITY;

-- 5. 创建时间调整表的安全策略
CREATE POLICY "Users can manage own adjustments" ON time_adjustments 
FOR ALL USING (auth.uid() = user_id);

-- 6. 创建用户时间约束表（存储用户的固定时间段）
CREATE TABLE IF NOT EXISTS user_time_constraints (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  constraint_type VARCHAR NOT NULL CHECK (constraint_type IN ('work', 'sleep', 'meal', 'commute', 'personal', 'break')),
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  days_of_week INTEGER[] DEFAULT '{1,2,3,4,5,6,7}', -- 1=Monday, 7=Sunday
  label VARCHAR NOT NULL,
  is_fixed BOOLEAN DEFAULT TRUE,
  priority INTEGER DEFAULT 5 CHECK (priority BETWEEN 1 AND 10),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 7. 为用户时间约束表启用行级安全
ALTER TABLE user_time_constraints ENABLE ROW LEVEL SECURITY;

-- 8. 创建用户时间约束表的安全策略
CREATE POLICY "Users can manage own constraints" ON user_time_constraints 
FOR ALL USING (auth.uid() = user_id);

-- 9. 创建任务时间变更历史表
CREATE TABLE IF NOT EXISTS task_time_changes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
  change_type VARCHAR NOT NULL CHECK (change_type IN ('created', 'rescheduled', 'extended', 'compressed', 'split')),
  original_start_time TIMESTAMP,
  original_end_time TIMESTAMP,
  new_start_time TIMESTAMP,
  new_end_time TIMESTAMP,
  reason VARCHAR,
  changed_by VARCHAR DEFAULT 'user' CHECK (changed_by IN ('user', 'system', 'algorithm')),
  created_at TIMESTAMP DEFAULT NOW()
);

-- 10. 为任务时间变更表启用行级安全
ALTER TABLE task_time_changes ENABLE ROW LEVEL SECURITY;

-- 11. 创建任务时间变更表的安全策略
CREATE POLICY "Users can view own task changes" ON task_time_changes 
FOR SELECT USING (
  auth.uid() = (SELECT user_id FROM tasks WHERE tasks.id = task_time_changes.task_id)
);

CREATE POLICY "System can insert task changes" ON task_time_changes 
FOR INSERT WITH CHECK (
  auth.uid() = (SELECT user_id FROM tasks WHERE tasks.id = task_time_changes.task_id)
);

-- 12. 创建触发器：自动记录任务时间变更
CREATE OR REPLACE FUNCTION log_task_time_change()
RETURNS TRIGGER AS $$
BEGIN
  -- 只在时间相关字段变更时记录
  IF (OLD.deadline IS DISTINCT FROM NEW.deadline) OR 
     (OLD.estimated_duration IS DISTINCT FROM NEW.estimated_duration) THEN
    
    INSERT INTO task_time_changes (
      task_id,
      change_type,
      original_start_time,
      original_end_time,
      new_start_time,
      new_end_time,
      reason,
      changed_by
    ) VALUES (
      NEW.id,
      'rescheduled',
      OLD.deadline,
      OLD.deadline + (OLD.estimated_duration || ' minutes')::INTERVAL,
      NEW.deadline,
      NEW.deadline + (NEW.estimated_duration || ' minutes')::INTERVAL,
      'Task time updated',
      'user'
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 13. 创建触发器
DROP TRIGGER IF EXISTS task_time_change_trigger ON tasks;
CREATE TRIGGER task_time_change_trigger
  AFTER UPDATE ON tasks
  FOR EACH ROW
  EXECUTE FUNCTION log_task_time_change();

-- 14. 创建用户偏好设置表
CREATE TABLE IF NOT EXISTS user_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  preferred_work_hours VARCHAR DEFAULT 'morning' CHECK (preferred_work_hours IN ('morning', 'afternoon', 'evening')),
  max_continuous_work INTEGER DEFAULT 120, -- 分钟
  break_interval INTEGER DEFAULT 25, -- 分钟
  notification_settings JSONB DEFAULT '{"email": true, "push": true, "desktop": false}',
  adjustment_strategy VARCHAR DEFAULT 'balanced' CHECK (adjustment_strategy IN ('conservative', 'balanced', 'aggressive')),
  auto_adjust_enabled BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 15. 为用户偏好表启用行级安全
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- 16. 创建用户偏好表的安全策略
CREATE POLICY "Users can manage own preferences" ON user_preferences 
FOR ALL USING (auth.uid() = user_id);

-- 17. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_time_adjustments_user_date ON time_adjustments(user_id, adjustment_date);
CREATE INDEX IF NOT EXISTS idx_user_time_constraints_user ON user_time_constraints(user_id);
CREATE INDEX IF NOT EXISTS idx_task_time_changes_task ON task_time_changes(task_id);
CREATE INDEX IF NOT EXISTS idx_tasks_user_deadline ON tasks(user_id, deadline);
CREATE INDEX IF NOT EXISTS idx_daily_stats_user_date ON daily_stats(user_id, date);

-- 18. 创建视图：用户时间利用率统计
CREATE OR REPLACE VIEW user_time_utilization AS
SELECT 
  u.id as user_id,
  u.email,
  COUNT(t.id) as total_tasks,
  COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_tasks,
  COUNT(CASE WHEN t.status = 'postponed' THEN 1 END) as postponed_tasks,
  AVG(t.postpone_count) as avg_postpone_count,
  SUM(CASE WHEN t.status = 'completed' THEN t.estimated_duration ELSE 0 END) as total_completed_time,
  COUNT(ta.id) as total_adjustments,
  AVG(ta.impact_score) as avg_adjustment_impact
FROM auth.users u
LEFT JOIN tasks t ON u.id = t.user_id
LEFT JOIN time_adjustments ta ON u.id = ta.user_id
WHERE t.created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY u.id, u.email;

-- 19. 创建函数：获取用户可用时间段
CREATE OR REPLACE FUNCTION get_user_available_time_slots(
  p_user_id UUID,
  p_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
  start_time TIME,
  end_time TIME,
  duration_minutes INTEGER,
  slot_type VARCHAR
) AS $$
BEGIN
  RETURN QUERY
  WITH time_constraints AS (
    SELECT 
      utc.start_time,
      utc.end_time,
      utc.constraint_type,
      utc.is_fixed
    FROM user_time_constraints utc
    WHERE utc.user_id = p_user_id
      AND EXTRACT(DOW FROM p_date) + 1 = ANY(utc.days_of_week)
  ),
  busy_slots AS (
    SELECT 
      tc.start_time,
      tc.end_time,
      'busy' as slot_type
    FROM time_constraints tc
    WHERE tc.is_fixed = true
  )
  SELECT 
    bs.end_time as start_time,
    LEAD(bs.start_time) OVER (ORDER BY bs.start_time) as end_time,
    EXTRACT(EPOCH FROM (LEAD(bs.start_time) OVER (ORDER BY bs.start_time) - bs.end_time))/60 as duration_minutes,
    'available'::VARCHAR as slot_type
  FROM busy_slots bs
  WHERE LEAD(bs.start_time) OVER (ORDER BY bs.start_time) IS NOT NULL
    AND EXTRACT(EPOCH FROM (LEAD(bs.start_time) OVER (ORDER BY bs.start_time) - bs.end_time))/60 >= 15;
END;
$$ LANGUAGE plpgsql;

-- 20. 插入默认用户偏好（为现有用户）
INSERT INTO user_preferences (user_id)
SELECT id FROM auth.users 
WHERE id NOT IN (SELECT user_id FROM user_preferences WHERE user_id IS NOT NULL)
ON CONFLICT (user_id) DO NOTHING;

-- 完成迁移
SELECT 'TimeManager 数据库迁移完成！' as status;
