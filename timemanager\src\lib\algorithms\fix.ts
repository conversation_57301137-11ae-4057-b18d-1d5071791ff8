import { Task, PostponedTaskAlert } from '@/types';
import { supabase } from '@/lib/supabase';

export class FixAlgorithm {
  // 推迟次数阈值
  private readonly POSTPONE_THRESHOLDS = {
    low: 1,
    medium: 3,
    high: 5,
    critical: 8
  };
  
  /**
   * 分析需要修复的推迟任务
   */
  async analyzePostponedTasks(userId: string): Promise<PostponedTaskAlert[]> {
    try {
      // 获取所有推迟的任务
      const postponedTasks = await this.getPostponedTasks(userId);
      
      return postponedTasks.map(task => {
        const urgencyLevel = this.calculateUrgencyLevel(task);
        const suggestion = this.generateFixSuggestion(task);
        const daysSinceCreated = this.calculateDaysSince(task.createdAt);
        
        return {
          task,
          postponeCount: task.postponeCount,
          daysSinceCreated,
          urgencyLevel,
          suggestion,
          shouldAlert: urgencyLevel !== 'low'
        };
      }).filter(alert => alert.shouldAlert); // 只返回需要提醒的任务
    } catch (error) {
      console.error('Error analyzing postponed tasks:', error);
      return [];
    }
  }
  
  /**
   * 计算任务的紧急程度
   */
  private calculateUrgencyLevel(task: Task): 'low' | 'medium' | 'high' | 'critical' {
    const { postponeCount, deadline } = task;
    const daysSinceDeadline = this.calculateDaysSince(deadline);
    const daysSinceCreated = this.calculateDaysSince(task.createdAt);
    
    // 综合考虑推迟次数、截止时间和创建时间
    let urgencyScore = 0;
    
    // 推迟次数评分
    if (postponeCount >= this.POSTPONE_THRESHOLDS.critical) urgencyScore += 4;
    else if (postponeCount >= this.POSTPONE_THRESHOLDS.high) urgencyScore += 3;
    else if (postponeCount >= this.POSTPONE_THRESHOLDS.medium) urgencyScore += 2;
    else if (postponeCount >= this.POSTPONE_THRESHOLDS.low) urgencyScore += 1;
    
    // 截止时间评分
    if (daysSinceDeadline > 0) urgencyScore += 3; // 已过期
    else if (daysSinceDeadline > -1) urgencyScore += 2; // 1天内到期
    else if (daysSinceDeadline > -3) urgencyScore += 1; // 3天内到期
    
    // 创建时间评分（任务存在时间过长）
    if (daysSinceCreated > 14) urgencyScore += 2; // 超过2周
    else if (daysSinceCreated > 7) urgencyScore += 1; // 超过1周
    
    // 重要性和紧急性加权
    if (task.importance >= 4) urgencyScore += 1;
    if (task.urgency >= 4) urgencyScore += 1;
    
    // 根据总分确定紧急程度
    if (urgencyScore >= 7) return 'critical';
    if (urgencyScore >= 5) return 'high';
    if (urgencyScore >= 3) return 'medium';
    return 'low';
  }
  
  /**
   * 生成修复建议
   */
  private generateFixSuggestion(task: Task): string {
    const urgencyLevel = this.calculateUrgencyLevel(task);
    const { postponeCount, category, estimatedDuration } = task;
    
    // 基于紧急程度的基础建议
    const baseSuggestions = {
      critical: "🚨 紧急处理：这个任务已经严重延期，建议立即处理或重新评估其必要性",
      high: "⚠️ 重点关注：建议将任务分解为更小的部分，或调整截止时间",
      medium: "💡 优化建议：可以设置更具体的时间安排或降低任务难度",
      low: "📝 轻微提醒：建议适当调整任务优先级或时间安排"
    };
    
    let suggestion = baseSuggestions[urgencyLevel];
    
    // 基于推迟次数的具体建议
    if (postponeCount >= 5) {
      suggestion += "\n• 考虑将任务分解为5-10分钟的小任务";
    } else if (postponeCount >= 3) {
      suggestion += "\n• 尝试番茄工作法，专注25分钟";
    }
    
    // 基于任务时长的建议
    if (estimatedDuration > 120) { // 超过2小时
      suggestion += "\n• 任务时间较长，建议分解为多个子任务";
    }
    
    // 基于分类的建议
    if (category === 'work') {
      suggestion += "\n• 工作任务：考虑在精力最好的时间段处理";
    } else if (category === 'improvement') {
      suggestion += "\n• 提升任务：可以设置学习奖励机制";
    } else if (category === 'entertainment') {
      suggestion += "\n• 娱乐任务：确保这确实是你想要的放松方式";
    }
    
    return suggestion;
  }
  
  /**
   * 获取推迟的任务
   */
  private async getPostponedTasks(userId: string): Promise<Task[]> {
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('user_id', userId)
      .gt('postpone_count', 0)
      .in('status', ['pending', 'postponed'])
      .order('postpone_count', { ascending: false });
    
    if (error) {
      console.error('Error fetching postponed tasks:', error);
      return [];
    }
    
    return data.map(task => ({
      id: task.id,
      userId: task.user_id,
      title: task.title,
      description: task.description,
      category: task.category,
      importance: task.importance,
      urgency: task.urgency,
      deadline: new Date(task.deadline),
      estimatedDuration: task.estimated_duration,
      status: task.status,
      postponeCount: task.postpone_count,
      createdAt: new Date(task.created_at),
      updatedAt: new Date(task.updated_at)
    }));
  }
  
  /**
   * 计算距离某个日期的天数
   */
  private calculateDaysSince(date: Date): number {
    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    return Math.floor(diffTime / (1000 * 60 * 60 * 24));
  }
  
  /**
   * 推迟任务
   */
  async postponeTask(taskId: string, reason?: string): Promise<void> {
    try {
      // 获取当前任务
      const { data: task, error: fetchError } = await supabase
        .from('tasks')
        .select('postpone_count')
        .eq('id', taskId)
        .single();
      
      if (fetchError) throw fetchError;
      
      // 更新推迟次数
      const { error: updateError } = await supabase
        .from('tasks')
        .update({
          postpone_count: (task.postpone_count || 0) + 1,
          status: 'postponed',
          updated_at: new Date().toISOString()
        })
        .eq('id', taskId);
      
      if (updateError) throw updateError;
      
      // 记录推迟历史（如果有历史表的话）
      // 这里可以扩展记录推迟原因等信息
      
    } catch (error) {
      console.error('Error postponing task:', error);
      throw new Error('Failed to postpone task');
    }
  }
  
  /**
   * 重置任务的推迟状态
   */
  async resetTaskPostponeStatus(taskId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('tasks')
        .update({
          postpone_count: 0,
          status: 'pending',
          updated_at: new Date().toISOString()
        })
        .eq('id', taskId);
      
      if (error) throw error;
    } catch (error) {
      console.error('Error resetting task postpone status:', error);
      throw new Error('Failed to reset task postpone status');
    }
  }
  
  /**
   * 获取推迟统计信息
   */
  async getPostponeStats(userId: string): Promise<{
    totalPostponedTasks: number;
    averagePostponeCount: number;
    mostPostponedCategory: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('postpone_count, category')
        .eq('user_id', userId)
        .gt('postpone_count', 0);
      
      if (error) throw error;
      
      if (!data || data.length === 0) {
        return {
          totalPostponedTasks: 0,
          averagePostponeCount: 0,
          mostPostponedCategory: 'work'
        };
      }
      
      const totalPostponedTasks = data.length;
      const averagePostponeCount = data.reduce((sum, task) => sum + task.postpone_count, 0) / totalPostponedTasks;
      
      // 统计各分类的推迟次数
      const categoryStats = data.reduce((acc, task) => {
        acc[task.category] = (acc[task.category] || 0) + task.postpone_count;
        return acc;
      }, {} as Record<string, number>);
      
      const mostPostponedCategory = Object.keys(categoryStats).reduce((a, b) => 
        categoryStats[a] > categoryStats[b] ? a : b
      );
      
      return {
        totalPostponedTasks,
        averagePostponeCount: Math.round(averagePostponeCount * 10) / 10,
        mostPostponedCategory
      };
    } catch (error) {
      console.error('Error getting postpone stats:', error);
      return {
        totalPostponedTasks: 0,
        averagePostponeCount: 0,
        mostPostponedCategory: 'work'
      };
    }
  }
}
