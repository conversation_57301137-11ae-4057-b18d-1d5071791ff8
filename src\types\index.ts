// 任务相关类型
export interface Task {
  id: string;
  userId: string;
  title: string;
  description?: string;
  category: 'work' | 'improvement' | 'entertainment';
  importance: 1 | 2 | 3 | 4 | 5;
  urgency: 1 | 2 | 3 | 4 | 5;
  deadline: Date;
  estimatedDuration: number; // 分钟
  status: 'pending' | 'in-progress' | 'completed' | 'postponed';
  postponeCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// 时间段类型
export interface TimeSlot {
  task: Task;
  startTime: Date;
  endTime: Date;
  isFixed: boolean;
}

// 今日规划类型
export interface DailySchedule {
  date: Date;
  timeSlots: TimeSlot[];
  totalTasks: number;
  estimatedDuration: number;
}

// 任务评分类型
export interface ScoredTask extends Task {
  score: number;
  quadrant: 1 | 2 | 3 | 4;
}

// 分类比例类型
export interface CategoryRatios {
  work: number;
  improvement: number;
  entertainment: number;
}

// 生活平衡分析类型
export interface BalanceAnalysis {
  workRatio: number;
  improvementRatio: number;
  entertainmentRatio: number;
  balanceScore: number;
  recommendation: string;
}

// 推迟任务提醒类型
export interface PostponedTaskAlert {
  task: Task;
  postponeCount: number;
  daysSinceCreated: number;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  suggestion: string;
  shouldAlert: boolean;
}

// 任务完成记录类型
export interface TaskCompletion {
  id: string;
  taskId: string;
  completedAt: Date;
  actualDuration: number;
  satisfactionScore: 1 | 2 | 3 | 4 | 5;
}

// 每日统计类型
export interface DailyStats {
  id: string;
  userId: string;
  date: Date;
  workTime: number;
  improvementTime: number;
  entertainmentTime: number;
  tasksCompleted: number;
  tasksPostponed: number;
  balanceScore: number;
}

// 用户配置类型
export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  timezone: string;
  workHours: {
    start: string;
    end: string;
  };
  categoryRatios: CategoryRatios;
  createdAt: Date;
  updatedAt: Date;
}

// API响应类型
export interface ApiResponse<T> {
  data: T;
  error?: string;
  success: boolean;
}

// 日历事件类型
export interface CalendarEvent {
  id?: string;
  title: string;
  description: string;
  startTime: Date;
  endTime: Date;
  category: string;
  reminders: number[];
}

// 推荐类型
export interface Recommendation {
  task: Task;
  recommendedSlot: TimeSlot;
  reason: string;
  confidence: number;
}
