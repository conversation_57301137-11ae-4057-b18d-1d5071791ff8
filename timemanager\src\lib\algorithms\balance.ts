import { CategoryRatios, BalanceAnalysis, DailyStats } from '@/types';
import { supabase } from '@/lib/supabase';

export class BalanceAlgorithm {
  // 理想的时间分配比例
  private readonly IDEAL_RATIOS: CategoryRatios = {
    work: 0.6,
    improvement: 0.25,
    entertainment: 0.15
  };
  
  /**
   * 分析用户的生活平衡状况
   */
  async analyzeWeeklyBalance(userId: string): Promise<BalanceAnalysis> {
    try {
      // 获取最近7天的统计数据
      const stats = await this.getDailyStats(userId, 7);
      
      if (stats.length === 0) {
        return {
          workRatio: 0,
          improvementRatio: 0,
          entertainmentRatio: 0,
          balanceScore: 0,
          recommendation: "开始记录你的任务来获得生活平衡分析 📊"
        };
      }
      
      // 计算总时间和各分类时间
      const totalTime = stats.reduce((sum, day) => 
        sum + day.workTime + day.improvementTime + day.entertainmentTime, 0
      );
      
      if (totalTime === 0) {
        return {
          workRatio: 0,
          improvementRatio: 0,
          entertainmentRatio: 0,
          balanceScore: 0,
          recommendation: "还没有完成任务记录，开始你的第一个任务吧！ 🚀"
        };
      }
      
      // 计算各分类的时间比例
      const ratios: CategoryRatios = {
        work: stats.reduce((sum, day) => sum + day.workTime, 0) / totalTime,
        improvement: stats.reduce((sum, day) => sum + day.improvementTime, 0) / totalTime,
        entertainment: stats.reduce((sum, day) => sum + day.entertainmentTime, 0) / totalTime
      };
      
      // 计算平衡分数
      const balanceScore = this.calculateBalanceScore(ratios);
      
      // 生成建议
      const recommendation = this.generateRecommendation(ratios, stats);
      
      return {
        workRatio: ratios.work,
        improvementRatio: ratios.improvement,
        entertainmentRatio: ratios.entertainment,
        balanceScore,
        recommendation
      };
    } catch (error) {
      console.error('Error analyzing balance:', error);
      throw new Error('Failed to analyze balance');
    }
  }
  
  /**
   * 计算生活平衡分数 (0-100)
   */
  private calculateBalanceScore(ratios: CategoryRatios): number {
    let score = 100;
    
    // 计算每个分类与理想比例的偏差
    Object.keys(this.IDEAL_RATIOS).forEach(category => {
      const ideal = this.IDEAL_RATIOS[category as keyof CategoryRatios];
      const actual = ratios[category as keyof CategoryRatios];
      const deviation = Math.abs(ideal - actual);
      
      // 偏差越大，扣分越多
      score -= deviation * 100;
    });
    
    return Math.max(0, Math.round(score));
  }
  
  /**
   * 生成个性化建议
   */
  private generateRecommendation(ratios: CategoryRatios, stats: DailyStats[]): string {
    const recommendations: string[] = [];
    
    // 分析工作时间
    if (ratios.work > 0.8) {
      recommendations.push("⚠️ 工作时间过长，建议增加休息和娱乐时间");
    } else if (ratios.work < 0.3) {
      recommendations.push("💼 工作时间较少，可以适当增加工作或学习时间");
    }
    
    // 分析提升时间
    if (ratios.improvement < 0.1) {
      recommendations.push("📚 建议安排一些学习或自我提升的活动");
    } else if (ratios.improvement > 0.4) {
      recommendations.push("🎯 学习时间充足，注意劳逸结合");
    }
    
    // 分析娱乐时间
    if (ratios.entertainment < 0.05) {
      recommendations.push("🎮 需要更多的放松和娱乐时间");
    } else if (ratios.entertainment > 0.3) {
      recommendations.push("⏰ 娱乐时间较多，可以适当增加工作或学习");
    }
    
    // 分析连续性
    const recentDays = stats.slice(-3); // 最近3天
    const hasConsistentWork = recentDays.every(day => day.workTime > 0);
    const hasConsistentImprovement = recentDays.every(day => day.improvementTime > 0);
    
    if (!hasConsistentWork) {
      recommendations.push("🔄 建议保持每日工作的连续性");
    }
    
    if (!hasConsistentImprovement) {
      recommendations.push("📈 建议每天安排一些自我提升时间");
    }
    
    // 如果没有特别的建议，给出正面反馈
    if (recommendations.length === 0) {
      const score = this.calculateBalanceScore(ratios);
      if (score >= 80) {
        return "✨ 生活平衡状态优秀，继续保持！";
      } else if (score >= 60) {
        return "👍 生活平衡状态良好，可以微调优化";
      } else {
        return "🎯 生活平衡有改善空间，建议关注时间分配";
      }
    }
    
    return recommendations.join(" • ");
  }
  
  /**
   * 获取用户的每日统计数据
   */
  private async getDailyStats(userId: string, days: number): Promise<DailyStats[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('daily_stats')
      .select('*')
      .eq('user_id', userId)
      .gte('date', startDate.toISOString().split('T')[0])
      .order('date', { ascending: true });
    
    if (error) {
      console.error('Error fetching daily stats:', error);
      return [];
    }
    
    return data.map(stat => ({
      id: stat.id,
      userId: stat.user_id,
      date: new Date(stat.date),
      workTime: stat.work_time,
      improvementTime: stat.improvement_time,
      entertainmentTime: stat.entertainment_time,
      tasksCompleted: stat.tasks_completed,
      tasksPostponed: stat.tasks_postponed,
      balanceScore: stat.balance_score || 0
    }));
  }
  
  /**
   * 更新今日统计数据
   */
  async updateTodayStats(userId: string, category: string, timeSpent: number): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    
    try {
      // 获取今日统计
      const { data: existing } = await supabase
        .from('daily_stats')
        .select('*')
        .eq('user_id', userId)
        .eq('date', today)
        .single();
      
      const updateData: any = {};
      
      if (category === 'work') {
        updateData.work_time = (existing?.work_time || 0) + timeSpent;
      } else if (category === 'improvement') {
        updateData.improvement_time = (existing?.improvement_time || 0) + timeSpent;
      } else if (category === 'entertainment') {
        updateData.entertainment_time = (existing?.entertainment_time || 0) + timeSpent;
      }
      
      if (existing) {
        // 更新现有记录
        await supabase
          .from('daily_stats')
          .update(updateData)
          .eq('id', existing.id);
      } else {
        // 创建新记录
        await supabase
          .from('daily_stats')
          .insert({
            user_id: userId,
            date: today,
            ...updateData
          });
      }
    } catch (error) {
      console.error('Error updating daily stats:', error);
    }
  }
  
  /**
   * 获取平衡状态的颜色指示
   */
  getBalanceStatusColor(score: number): string {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  }
  
  /**
   * 获取平衡状态的描述
   */
  getBalanceStatusText(score: number): string {
    if (score >= 80) return '优秀';
    if (score >= 60) return '良好';
    if (score >= 40) return '一般';
    return '需要改善';
  }
}
