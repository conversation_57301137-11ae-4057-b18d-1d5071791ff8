import { create } from 'zustand';
import { Task, DailySchedule, BalanceAnalysis, PostponedTaskAlert } from '@/shared';
import { supabase } from '@/lib/supabase';
import { getDefaultPlanningService } from '@/domains/intelligent-planning';

interface TaskState {
  // State
  tasks: Task[];
  dailySchedule: DailySchedule | null;
  balanceAnalysis: BalanceAnalysis | null;
  postponedAlerts: PostponedTaskAlert[];
  loading: boolean;
  error: string | null;
  
  // Planning Service
  planningService: ReturnType<typeof getDefaultPlanningService>;
  
  // Actions
  fetchTasks: (userId: string) => Promise<void>;
  createTask: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateTask: (id: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
  completeTask: (id: string, actualDuration?: number, satisfaction?: number) => Promise<void>;
  postponeTask: (id: string, reason?: string) => Promise<void>;
  
  // Algorithm actions
  generateDailySchedule: (userId: string) => Promise<void>;
  analyzeBalance: (userId: string) => Promise<void>;
  analyzePostponedTasks: (userId: string) => Promise<void>;
  
  // Utility actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useTaskStore = create<TaskState>((set, get) => ({
  // Initial state
  tasks: [],
  dailySchedule: null,
  balanceAnalysis: null,
  postponedAlerts: [],
  loading: false,
  error: null,
  
  // Planning service instance
  planningService: getDefaultPlanningService(),
  
  // Fetch tasks
  fetchTasks: async (userId: string) => {
    try {
      set({ loading: true, error: null });
      
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      const tasks: Task[] = data.map(task => ({
        id: task.id,
        userId: task.user_id,
        title: task.title,
        description: task.description,
        category: task.category,
        importance: task.importance,
        urgency: task.urgency,
        deadline: new Date(task.deadline),
        estimatedDuration: task.estimated_duration,
        status: task.status,
        postponeCount: task.postpone_count,
        createdAt: new Date(task.created_at),
        updatedAt: new Date(task.updated_at)
      }));
      
      set({ tasks, loading: false });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tasks';
      set({ error: errorMessage, loading: false });
    }
  },
  
  // Create task
  createTask: async (taskData) => {
    try {
      set({ loading: true, error: null });
      
      const { data, error } = await supabase
        .from('tasks')
        .insert({
          user_id: taskData.userId,
          title: taskData.title,
          description: taskData.description,
          category: taskData.category,
          importance: taskData.importance,
          urgency: taskData.urgency,
          deadline: taskData.deadline.toISOString(),
          estimated_duration: taskData.estimatedDuration,
          status: taskData.status,
          postpone_count: taskData.postponeCount
        })
        .select()
        .single();
      
      if (error) throw error;
      
      const newTask: Task = {
        id: data.id,
        userId: data.user_id,
        title: data.title,
        description: data.description,
        category: data.category,
        importance: data.importance,
        urgency: data.urgency,
        deadline: new Date(data.deadline),
        estimatedDuration: data.estimated_duration,
        status: data.status,
        postponeCount: data.postpone_count,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at)
      };
      
      set(state => ({
        tasks: [newTask, ...state.tasks],
        loading: false
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create task';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },
  
  // Update task
  updateTask: async (id: string, updates: Partial<Task>) => {
    try {
      set({ loading: true, error: null });
      
      const updateData: any = {};
      if (updates.title) updateData.title = updates.title;
      if (updates.description !== undefined) updateData.description = updates.description;
      if (updates.category) updateData.category = updates.category;
      if (updates.importance) updateData.importance = updates.importance;
      if (updates.urgency) updateData.urgency = updates.urgency;
      if (updates.deadline) updateData.deadline = updates.deadline.toISOString();
      if (updates.estimatedDuration) updateData.estimated_duration = updates.estimatedDuration;
      if (updates.status) updateData.status = updates.status;
      if (updates.postponeCount !== undefined) updateData.postpone_count = updates.postponeCount;
      
      updateData.updated_at = new Date().toISOString();
      
      const { error } = await supabase
        .from('tasks')
        .update(updateData)
        .eq('id', id);
      
      if (error) throw error;
      
      set(state => ({
        tasks: state.tasks.map(task => 
          task.id === id ? { ...task, ...updates, updatedAt: new Date() } : task
        ),
        loading: false
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update task';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },
  
  // Delete task
  deleteTask: async (id: string) => {
    try {
      set({ loading: true, error: null });
      
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      set(state => ({
        tasks: state.tasks.filter(task => task.id !== id),
        loading: false
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete task';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },
  
  // Complete task
  completeTask: async (id: string, actualDuration?: number, satisfaction?: number) => {
    try {
      const { updateTask, balanceAlgorithm } = get();
      const task = get().tasks.find(t => t.id === id);
      
      if (!task) throw new Error('Task not found');
      
      // Update task status
      await updateTask(id, { status: 'completed' });
      
      // Record completion
      if (actualDuration && satisfaction) {
        await supabase
          .from('task_completions')
          .insert({
            task_id: id,
            actual_duration: actualDuration,
            satisfaction_score: satisfaction
          });
      }
      
      // Update daily stats
      await balanceAlgorithm.updateTodayStats(
        task.userId, 
        task.category, 
        actualDuration || task.estimatedDuration
      );
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to complete task';
      set({ error: errorMessage });
      throw error;
    }
  },
  
  // Postpone task
  postponeTask: async (id: string, reason?: string) => {
    try {
      const { fixAlgorithm } = get();
      await fixAlgorithm.postponeTask(id, reason);
      
      // Refresh tasks
      const task = get().tasks.find(t => t.id === id);
      if (task) {
        await get().fetchTasks(task.userId);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to postpone task';
      set({ error: errorMessage });
      throw error;
    }
  },
  
  // Generate daily schedule
  generateDailySchedule: async (userId: string) => {
    try {
      set({ loading: true, error: null });

      const { tasks, planningService } = get();
      const userTasks = tasks.filter(task => task.userId === userId);

      // 获取用户时间配置
      let userTimeConfig = null;
      try {
        const { data: userProfile } = await supabase
          .from('user_profiles')
          .select('time_config')
          .eq('id', userId)
          .single();

        userTimeConfig = userProfile?.time_config;
      } catch (error) {
        console.log('No user time config found, using defaults');
      }

      // 使用新的规划服务
      const result = await planningService.generateDailyPlan(userId, userTasks, userTimeConfig);

      if (result.success) {
        set({
          dailySchedule: result.data.schedule,
          loading: false
        });
      } else {
        set({
          error: result.error || 'Failed to generate schedule',
          loading: false
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate schedule';
      set({ error: errorMessage, loading: false });
    }
  },
  
  // Analyze balance
  analyzeBalance: async (userId: string) => {
    try {
      set({ loading: true, error: null });

      const { planningService } = get();
      const result = await planningService.getBalanceAnalysis(userId);

      if (result.success) {
        set({ balanceAnalysis: result.data, loading: false });
      } else {
        set({
          error: result.error || 'Failed to analyze balance',
          loading: false
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze balance';
      set({ error: errorMessage, loading: false });
    }
  },
  
  // Analyze postponed tasks
  analyzePostponedTasks: async (userId: string) => {
    try {
      set({ loading: true, error: null });

      const { tasks, planningService } = get();
      const userTasks = tasks.filter(task => task.userId === userId);
      const result = await planningService.getPostponedTasksAnalysis(userTasks);

      if (result.success) {
        set({ postponedAlerts: result.data.alerts, loading: false });
      } else {
        set({
          error: result.error || 'Failed to analyze postponed tasks',
          loading: false
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze postponed tasks';
      set({ error: errorMessage, loading: false });
    }
  },
  
  // Utility actions
  setLoading: (loading: boolean) => set({ loading }),
  setError: (error: string | null) => set({ error }),
  clearError: () => set({ error: null })
}));
