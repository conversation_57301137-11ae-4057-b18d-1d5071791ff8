'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/useAuthStore';
import { useTaskStore } from '@/store/useTaskStore';
import { Calendar, Clock, Target, TrendingUp, Plus, Settings } from 'lucide-react';

export default function Dashboard() {
  const router = useRouter();
  const { user, signOut } = useAuthStore();
  const { 
    tasks, 
    dailySchedule, 
    balanceAnalysis, 
    postponedAlerts,
    fetchTasks,
    generateDailySchedule,
    analyzeBalance,
    analyzePostponedTasks,
    loading
  } = useTaskStore();

  useEffect(() => {
    if (!user) {
      router.push('/auth/signin');
      return;
    }

    // 初始化数据
    const initializeData = async () => {
      try {
        await fetchTasks(user.id);
        await generateDailySchedule(user.id);
        await analyzeBalance(user.id);
        await analyzePostponedTasks(user.id);
      } catch (error) {
        console.error('Failed to initialize dashboard data:', error);
      }
    };

    initializeData();
  }, [user, router, fetchTasks, generateDailySchedule, analyzeBalance, analyzePostponedTasks]);

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/auth/signin');
    } catch (error) {
      console.error('Sign out failed:', error);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  const todayTasks = tasks.filter(task => {
    const today = new Date();
    const taskDate = new Date(task.deadline);
    return taskDate.toDateString() === today.toDateString() && task.status !== 'completed';
  });

  const completedToday = tasks.filter(task => {
    const today = new Date();
    const taskDate = new Date(task.updatedAt);
    return taskDate.toDateString() === today.toDateString() && task.status === 'completed';
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">TimeManager</h1>
              <span className="ml-3 text-sm text-gray-500">智能时间规划助手</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">欢迎，{user.email}</span>
              <button
                onClick={() => router.push('/settings')}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-md"
              >
                <Settings className="h-5 w-5" />
              </button>
              <button
                onClick={handleSignOut}
                className="text-sm text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md border border-gray-300 hover:bg-gray-50"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Target className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">今日任务</p>
                <p className="text-2xl font-semibold text-gray-900">{todayTasks.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">已完成</p>
                <p className="text-2xl font-semibold text-gray-900">{completedToday.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">生活平衡</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {balanceAnalysis?.balanceScore || 0}分
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">推迟提醒</p>
                <p className="text-2xl font-semibold text-gray-900">{postponedAlerts.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => router.push('/tasks/new')}
                className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-indigo-500 hover:bg-indigo-50 transition-colors"
              >
                <Plus className="h-6 w-6 text-gray-400 mr-2" />
                <span className="text-gray-600">添加新任务</span>
              </button>
              
              <button
                onClick={() => router.push('/planning')}
                className="flex items-center justify-center p-4 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                <Calendar className="h-6 w-6 mr-2" />
                <span>查看今日规划</span>
              </button>
              
              <button
                onClick={() => router.push('/tasks')}
                className="flex items-center justify-center p-4 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <Target className="h-6 w-6 mr-2" />
                <span>管理所有任务</span>
              </button>
            </div>
          </div>
        </div>

        {/* Today's Schedule Preview */}
        {dailySchedule && dailySchedule.timeSlots.length > 0 && (
          <div className="mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">今日规划预览</h2>
              <div className="space-y-3">
                {dailySchedule.timeSlots.slice(0, 3).map((slot, index) => (
                  <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0 w-20 text-sm text-gray-500">
                      {slot.startTime.toLocaleTimeString('zh-CN', { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </div>
                    <div className="ml-4 flex-1">
                      <p className="font-medium text-gray-900">{slot.task.title}</p>
                      <p className="text-sm text-gray-500">{slot.task.category}</p>
                    </div>
                    <div className="flex-shrink-0">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {slot.task.estimatedDuration}分钟
                      </span>
                    </div>
                  </div>
                ))}
                {dailySchedule.timeSlots.length > 3 && (
                  <div className="text-center">
                    <button
                      onClick={() => router.push('/planning')}
                      className="text-indigo-600 hover:text-indigo-500 text-sm font-medium"
                    >
                      查看完整规划 ({dailySchedule.timeSlots.length - 3} 个更多任务)
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Balance Analysis */}
        {balanceAnalysis && (
          <div className="mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">生活平衡分析</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <p className="text-sm text-gray-500">工作</p>
                  <p className="text-2xl font-semibold text-blue-600">
                    {Math.round(balanceAnalysis.workRatio * 100)}%
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-500">提升</p>
                  <p className="text-2xl font-semibold text-green-600">
                    {Math.round(balanceAnalysis.improvementRatio * 100)}%
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-500">娱乐</p>
                  <p className="text-2xl font-semibold text-purple-600">
                    {Math.round(balanceAnalysis.entertainmentRatio * 100)}%
                  </p>
                </div>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-700">{balanceAnalysis.recommendation}</p>
              </div>
            </div>
          </div>
        )}

        {/* Postponed Tasks Alert */}
        {postponedAlerts.length > 0 && (
          <div className="mb-8">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-yellow-800 mb-4">⚠️ 推迟任务提醒</h2>
              <div className="space-y-3">
                {postponedAlerts.slice(0, 2).map((alert, index) => (
                  <div key={index} className="bg-white rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium text-gray-900">{alert.task.title}</p>
                        <p className="text-sm text-gray-500">
                          已推迟 {alert.postponeCount} 次 • {alert.daysSinceCreated} 天前创建
                        </p>
                      </div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        alert.urgencyLevel === 'critical' ? 'bg-red-100 text-red-800' :
                        alert.urgencyLevel === 'high' ? 'bg-orange-100 text-orange-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {alert.urgencyLevel}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">{alert.suggestion}</p>
                  </div>
                ))}
                {postponedAlerts.length > 2 && (
                  <div className="text-center">
                    <button
                      onClick={() => router.push('/tasks?filter=postponed')}
                      className="text-yellow-700 hover:text-yellow-600 text-sm font-medium"
                    >
                      查看所有推迟任务 ({postponedAlerts.length - 2} 个更多)
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {loading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="text-gray-500 mt-2">正在加载数据...</p>
          </div>
        )}
      </main>
    </div>
  );
}
