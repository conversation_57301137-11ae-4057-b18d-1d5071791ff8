{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\r\n\r\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\r\n\r\n// 检查是否配置了有效的Supabase环境变量\r\nconst isSupabaseConfigured = supabaseUrl &&\r\n  supabaseAnonKey &&\r\n  supabaseUrl !== 'your_supabase_url_here' &&\r\n  supabaseAnonKey !== 'your_supabase_anon_key_here' &&\r\n  supabaseUrl.startsWith('https://');\r\n\r\nlet supabaseClient: any = null;\r\n\r\nif (isSupabaseConfigured) {\r\n  supabaseClient = createClient(supabaseUrl!, supabaseAnonKey!, {\r\n    auth: {\r\n      autoRefreshToken: true,\r\n      persistSession: true,\r\n      detectSessionInUrl: true\r\n    },\r\n    realtime: {\r\n      params: {\r\n        eventsPerSecond: 10\r\n      }\r\n    }\r\n  });\r\n} else {\r\n  console.warn('⚠️ Supabase not configured. Running in development mode without database.');\r\n  // 创建一个模拟的Supabase客户端用于开发\r\n  supabaseClient = {\r\n    auth: {\r\n      signUp: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\r\n      signInWithPassword: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\r\n      signOut: () => Promise.resolve({ error: null }),\r\n      getUser: () => Promise.resolve({ data: { user: null }, error: null }),\r\n      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } })\r\n    },\r\n    from: () => ({\r\n      select: () => ({ eq: () => ({ order: () => Promise.resolve({ data: [], error: null }) }) }),\r\n      insert: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\r\n      update: () => Promise.resolve({ error: new Error('Supabase not configured') }),\r\n      delete: () => Promise.resolve({ error: new Error('Supabase not configured') })\r\n    })\r\n  };\r\n}\r\n\r\nexport const supabase = supabaseClient;\r\n\r\n// 数据库类型定义\r\nexport interface Database {\r\n  public: {\r\n    Tables: {\r\n      user_profiles: {\r\n        Row: {\r\n          id: string;\r\n          email: string;\r\n          name: string | null;\r\n          timezone: string;\r\n          work_hours: {\r\n            start: string;\r\n            end: string;\r\n            days?: string[];\r\n          };\r\n          category_ratios: {\r\n            work: number;\r\n            improvement: number;\r\n            entertainment: number;\r\n          };\r\n          time_config: any; // 详细的时间配置\r\n          onboarding_completed: boolean;\r\n          created_at: string;\r\n          updated_at: string;\r\n        };\r\n        Insert: {\r\n          id: string;\r\n          email: string;\r\n          name?: string | null;\r\n          timezone?: string;\r\n          work_hours?: {\r\n            start: string;\r\n            end: string;\r\n            days?: string[];\r\n          };\r\n          category_ratios?: {\r\n            work: number;\r\n            improvement: number;\r\n            entertainment: number;\r\n          };\r\n          time_config?: any;\r\n          onboarding_completed?: boolean;\r\n        };\r\n        Update: {\r\n          name?: string | null;\r\n          timezone?: string;\r\n          work_hours?: {\r\n            start: string;\r\n            end: string;\r\n            days?: string[];\r\n          };\r\n          category_ratios?: {\r\n            work: number;\r\n            improvement: number;\r\n            entertainment: number;\r\n          };\r\n          time_config?: any;\r\n          onboarding_completed?: boolean;\r\n        };\r\n      };\r\n      tasks: {\r\n        Row: {\r\n          id: string;\r\n          user_id: string;\r\n          title: string;\r\n          description: string | null;\r\n          category: 'work' | 'improvement' | 'entertainment';\r\n          importance: number;\r\n          urgency: number;\r\n          deadline: string;\r\n          estimated_duration: number;\r\n          status: 'pending' | 'in-progress' | 'completed' | 'postponed';\r\n          postpone_count: number;\r\n          created_at: string;\r\n          updated_at: string;\r\n        };\r\n        Insert: {\r\n          user_id: string;\r\n          title: string;\r\n          description?: string | null;\r\n          category: 'work' | 'improvement' | 'entertainment';\r\n          importance: number;\r\n          urgency: number;\r\n          deadline: string;\r\n          estimated_duration: number;\r\n          status?: 'pending' | 'in-progress' | 'completed' | 'postponed';\r\n          postpone_count?: number;\r\n        };\r\n        Update: {\r\n          title?: string;\r\n          description?: string | null;\r\n          category?: 'work' | 'improvement' | 'entertainment';\r\n          importance?: number;\r\n          urgency?: number;\r\n          deadline?: string;\r\n          estimated_duration?: number;\r\n          status?: 'pending' | 'in-progress' | 'completed' | 'postponed';\r\n          postpone_count?: number;\r\n        };\r\n      };\r\n      task_completions: {\r\n        Row: {\r\n          id: string;\r\n          task_id: string;\r\n          completed_at: string;\r\n          actual_duration: number;\r\n          satisfaction_score: number;\r\n        };\r\n        Insert: {\r\n          task_id: string;\r\n          actual_duration: number;\r\n          satisfaction_score: number;\r\n        };\r\n        Update: {\r\n          actual_duration?: number;\r\n          satisfaction_score?: number;\r\n        };\r\n      };\r\n      daily_stats: {\r\n        Row: {\r\n          id: string;\r\n          user_id: string;\r\n          date: string;\r\n          work_time: number;\r\n          improvement_time: number;\r\n          entertainment_time: number;\r\n          tasks_completed: number;\r\n          tasks_postponed: number;\r\n          balance_score: number | null;\r\n        };\r\n        Insert: {\r\n          user_id: string;\r\n          date: string;\r\n          work_time?: number;\r\n          improvement_time?: number;\r\n          entertainment_time?: number;\r\n          tasks_completed?: number;\r\n          tasks_postponed?: number;\r\n          balance_score?: number | null;\r\n        };\r\n        Update: {\r\n          work_time?: number;\r\n          improvement_time?: number;\r\n          entertainment_time?: number;\r\n          tasks_completed?: number;\r\n          tasks_postponed?: number;\r\n          balance_score?: number | null;\r\n        };\r\n      };\r\n    };\r\n  };\r\n}\r\n\r\n// 认证相关工具函数\r\nexport const auth = {\r\n  signUp: async (email: string, password: string) => {\r\n    return await supabase.auth.signUp({ email, password });\r\n  },\r\n  \r\n  signIn: async (email: string, password: string) => {\r\n    return await supabase.auth.signInWithPassword({ email, password });\r\n  },\r\n  \r\n  signOut: async () => {\r\n    return await supabase.auth.signOut();\r\n  },\r\n  \r\n  getCurrentUser: async () => {\r\n    const { data: { user } } = await supabase.auth.getUser();\r\n    return user;\r\n  },\r\n  \r\n  onAuthStateChange: (callback: (event: string, session: any) => void) => {\r\n    return supabase.auth.onAuthStateChange(callback);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;AAEoB;AAFpB;;AAEA,MAAM,cAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB;AACxD,MAAM,kBAAkB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B;AAEjE,yBAAyB;AACzB,MAAM,uBAAuB,eAC3B,mBACA,gBAAgB,4BAChB,oBAAoB,iCACpB,YAAY,UAAU,CAAC;AAEzB,IAAI,iBAAsB;AAE1B,IAAI,sBAAsB;IACxB,iBAAiB,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAc,iBAAkB;QAC5D,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;YAChB,oBAAoB;QACtB;QACA,UAAU;YACR,QAAQ;gBACN,iBAAiB;YACnB;QACF;IACF;AACF,OAAO;IACL,QAAQ,IAAI,CAAC;IACb,yBAAyB;IACzB,iBAAiB;QACf,MAAM;YACJ,QAAQ,IAAM,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO,IAAI,MAAM;gBAA2B;YACxF,oBAAoB,IAAM,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO,IAAI,MAAM;gBAA2B;YACpG,SAAS,IAAM,QAAQ,OAAO,CAAC;oBAAE,OAAO;gBAAK;YAC7C,SAAS,IAAM,QAAQ,OAAO,CAAC;oBAAE,MAAM;wBAAE,MAAM;oBAAK;oBAAG,OAAO;gBAAK;YACnE,mBAAmB,IAAM,CAAC;oBAAE,MAAM;wBAAE,cAAc;4BAAE,aAAa,KAAO;wBAAE;oBAAE;gBAAE,CAAC;QACjF;QACA,MAAM,IAAM,CAAC;gBACX,QAAQ,IAAM,CAAC;wBAAE,IAAI,IAAM,CAAC;gCAAE,OAAO,IAAM,QAAQ,OAAO,CAAC;wCAAE,MAAM,EAAE;wCAAE,OAAO;oCAAK;4BAAG,CAAC;oBAAE,CAAC;gBAC1F,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;gBAC5E,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;YAC9E,CAAC;IACH;AACF;AAEO,MAAM,WAAW;AA4JjB,MAAM,OAAO;IAClB,QAAQ,OAAO,OAAe;QAC5B,OAAO,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAAE;YAAO;QAAS;IACtD;IAEA,QAAQ,OAAO,OAAe;QAC5B,OAAO,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAAE;YAAO;QAAS;IAClE;IAEA,SAAS;QACP,OAAO,MAAM,SAAS,IAAI,CAAC,OAAO;IACpC;IAEA,gBAAgB;QACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACtD,OAAO;IACT;IAEA,mBAAmB,CAAC;QAClB,OAAO,SAAS,IAAI,CAAC,iBAAiB,CAAC;IACzC;AACF", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/store/useAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand';\r\nimport { persist } from 'zustand/middleware';\r\nimport { User } from '@supabase/supabase-js';\r\nimport { auth } from '@/lib/supabase';\r\n\r\ninterface AuthState {\r\n  user: User | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  \r\n  // Actions\r\n  signIn: (email: string, password: string) => Promise<void>;\r\n  signUp: (email: string, password: string) => Promise<void>;\r\n  signOut: () => Promise<void>;\r\n  setUser: (user: User | null) => void;\r\n  setLoading: (loading: boolean) => void;\r\n  setError: (error: string | null) => void;\r\n  clearError: () => void;\r\n}\r\n\r\nexport const useAuthStore = create<AuthState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      user: null,\r\n      loading: false,\r\n      error: null,\r\n      \r\n      signIn: async (email: string, password: string) => {\r\n        try {\r\n          set({ loading: true, error: null });\r\n          \r\n          const { data, error } = await auth.signIn(email, password);\r\n          \r\n          if (error) {\r\n            throw new Error(error.message);\r\n          }\r\n          \r\n          if (data.user) {\r\n            set({ user: data.user, loading: false });\r\n          }\r\n        } catch (error) {\r\n          const errorMessage = error instanceof Error ? error.message : 'Sign in failed';\r\n          set({ error: errorMessage, loading: false });\r\n          throw error;\r\n        }\r\n      },\r\n      \r\n      signUp: async (email: string, password: string) => {\r\n        try {\r\n          set({ loading: true, error: null });\r\n          \r\n          const { data, error } = await auth.signUp(email, password);\r\n          \r\n          if (error) {\r\n            throw new Error(error.message);\r\n          }\r\n          \r\n          if (data.user) {\r\n            set({ user: data.user, loading: false });\r\n          }\r\n        } catch (error) {\r\n          const errorMessage = error instanceof Error ? error.message : 'Sign up failed';\r\n          set({ error: errorMessage, loading: false });\r\n          throw error;\r\n        }\r\n      },\r\n      \r\n      signOut: async () => {\r\n        try {\r\n          set({ loading: true, error: null });\r\n          \r\n          const { error } = await auth.signOut();\r\n          \r\n          if (error) {\r\n            throw new Error(error.message);\r\n          }\r\n          \r\n          set({ user: null, loading: false });\r\n        } catch (error) {\r\n          const errorMessage = error instanceof Error ? error.message : 'Sign out failed';\r\n          set({ error: errorMessage, loading: false });\r\n          throw error;\r\n        }\r\n      },\r\n      \r\n      setUser: (user: User | null) => {\r\n        set({ user });\r\n      },\r\n      \r\n      setLoading: (loading: boolean) => {\r\n        set({ loading });\r\n      },\r\n      \r\n      setError: (error: string | null) => {\r\n        set({ error });\r\n      },\r\n      \r\n      clearError: () => {\r\n        set({ error: null });\r\n      }\r\n    }),\r\n    {\r\n      name: 'timemanager-auth',\r\n      partialize: (state) => ({\r\n        user: state.user\r\n      }),\r\n      // 使用 localStorage 持久化\r\n      storage: {\r\n        getItem: (name) => {\r\n          if (typeof window !== 'undefined') {\r\n            const value = localStorage.getItem(name);\r\n            return value ? JSON.parse(value) : null;\r\n          }\r\n          return null;\r\n        },\r\n        setItem: (name, value) => {\r\n          if (typeof window !== 'undefined') {\r\n            localStorage.setItem(name, JSON.stringify(value));\r\n          }\r\n        },\r\n        removeItem: (name) => {\r\n          if (typeof window !== 'undefined') {\r\n            localStorage.removeItem(name);\r\n          }\r\n        },\r\n      },\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAiBO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,SAAS;QACT,OAAO;QAEP,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OAAO;gBAEjD,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,IAAI,KAAK,IAAI,EAAE;oBACb,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,SAAS;oBAAM;gBACxC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OAAO;gBAEjD,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,IAAI,KAAK,IAAI,EAAE;oBACb,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,SAAS;oBAAM;gBACxC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,SAAS;YACP,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,OAAI,CAAC,OAAO;gBAEpC,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,IAAI;oBAAE,MAAM;oBAAM,SAAS;gBAAM;YACnC,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;YAAK;QACb;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;YAAQ;QAChB;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;QAClB,CAAC;IACD,sBAAsB;IACtB,SAAS;QACP,SAAS,CAAC;YACR,wCAAmC;gBACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,OAAO,QAAQ,KAAK,KAAK,CAAC,SAAS;YACrC;;QAEF;QACA,SAAS,CAAC,MAAM;YACd,wCAAmC;gBACjC,aAAa,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC;YAC5C;QACF;QACA,YAAY,CAAC;YACX,wCAAmC;gBACjC,aAAa,UAAU,CAAC;YAC1B;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/components/AuthListener.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { useAuthStore } from '@/store/useAuthStore';\r\nimport { supabase } from '@/lib/supabase';\r\n\r\ninterface AuthListenerProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport default function AuthListener({ children }: AuthListenerProps) {\r\n  const { setUser } = useAuthStore();\r\n  const [isMounted, setIsMounted] = useState(false);\r\n\r\n  // 防止水合错误\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!isMounted) return;\r\n\r\n    console.log('🎧 AuthListener: Setting up auth state listener...');\r\n\r\n    // 监听 Supabase 认证状态变化\r\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\r\n      (event, session) => {\r\n        console.log('🔄 Auth state changed:', event, session?.user?.id || 'none');\r\n        \r\n        // 同步认证状态到 Zustand store\r\n        setUser(session?.user || null);\r\n        \r\n        // 记录状态变化\r\n        if (event === 'SIGNED_IN') {\r\n          console.log('✅ User signed in:', session?.user?.email);\r\n        } else if (event === 'SIGNED_OUT') {\r\n          console.log('👋 User signed out');\r\n        } else if (event === 'TOKEN_REFRESHED') {\r\n          console.log('🔄 Token refreshed for:', session?.user?.email);\r\n        }\r\n      }\r\n    );\r\n\r\n    return () => {\r\n      console.log('🎧 AuthListener: Cleaning up auth listener...');\r\n      subscription.unsubscribe();\r\n    };\r\n  }, [isMounted, setUser]);\r\n\r\n  // 在客户端挂载前不渲染任何内容，避免水合错误\r\n  if (!isMounted) {\r\n    return null;\r\n  }\r\n\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAClE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,aAAa;QACf;iCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,WAAW;YAEhB,QAAQ,GAAG,CAAC;YAEZ,qBAAqB;YACrB,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,CAAC,OAAO;oBACN,QAAQ,GAAG,CAAC,0BAA0B,OAAO,SAAS,MAAM,MAAM;oBAElE,wBAAwB;oBACxB,QAAQ,SAAS,QAAQ;oBAEzB,SAAS;oBACT,IAAI,UAAU,aAAa;wBACzB,QAAQ,GAAG,CAAC,qBAAqB,SAAS,MAAM;oBAClD,OAAO,IAAI,UAAU,cAAc;wBACjC,QAAQ,GAAG,CAAC;oBACd,OAAO,IAAI,UAAU,mBAAmB;wBACtC,QAAQ,GAAG,CAAC,2BAA2B,SAAS,MAAM;oBACxD;gBACF;;YAGF;0CAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,aAAa,WAAW;gBAC1B;;QACF;iCAAG;QAAC;QAAW;KAAQ;IAEvB,wBAAwB;IACxB,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GA7CwB;;QACF,+HAAA,CAAA,eAAY;;;KADV", "debugId": null}}]}