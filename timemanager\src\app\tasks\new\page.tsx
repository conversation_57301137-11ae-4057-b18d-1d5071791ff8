'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/useAuthStore';
import { useTaskStore } from '@/store/useTaskStore';
import { ArrowLeft, Save } from 'lucide-react';

export default function NewTask() {
  const router = useRouter();
  const { user } = useAuthStore();
  const { createTask, loading } = useTaskStore();
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'work' as 'work' | 'improvement' | 'entertainment',
    importance: 3,
    urgency: 3,
    deadline: '',
    estimatedDuration: 60
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      router.push('/auth/signin');
      return;
    }

    try {
      await createTask({
        userId: user.id,
        title: formData.title,
        description: formData.description,
        category: formData.category,
        importance: formData.importance as 1 | 2 | 3 | 4 | 5,
        urgency: formData.urgency as 1 | 2 | 3 | 4 | 5,
        deadline: new Date(formData.deadline),
        estimatedDuration: formData.estimatedDuration,
        status: 'pending',
        postponeCount: 0
      });
      
      router.push('/dashboard');
    } catch (error) {
      console.error('Failed to create task:', error);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseInt(value) : value
    }));
  };

  // 设置默认截止时间为明天
  const getDefaultDeadline = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(18, 0, 0, 0); // 默认下午6点
    return tomorrow.toISOString().slice(0, 16); // YYYY-MM-DDTHH:MM format
  };

  if (!formData.deadline) {
    setFormData(prev => ({ ...prev, deadline: getDefaultDeadline() }));
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <button
              onClick={() => router.back()}
              className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
            >
              <ArrowLeft className="h-5 w-5 mr-1" />
              返回
            </button>
            <h1 className="text-xl font-semibold text-gray-900">创建新任务</h1>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 任务标题 */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                任务标题 *
              </label>
              <input
                type="text"
                id="title"
                name="title"
                required
                value={formData.title}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="请输入任务标题"
              />
            </div>

            {/* 任务描述 */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                任务描述
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                value={formData.description}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="请输入任务描述（可选）"
              />
            </div>

            {/* 任务分类 */}
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                任务分类 *
              </label>
              <select
                id="category"
                name="category"
                required
                value={formData.category}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="work">工作</option>
                <option value="improvement">提升</option>
                <option value="entertainment">娱乐</option>
              </select>
              <p className="mt-1 text-sm text-gray-500">
                工作：日常工作任务 | 提升：学习和自我提升 | 娱乐：休闲和放松
              </p>
            </div>

            {/* 重要性和紧急性 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="importance" className="block text-sm font-medium text-gray-700 mb-2">
                  重要性 *
                </label>
                <select
                  id="importance"
                  name="importance"
                  required
                  value={formData.importance}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value={1}>1 - 不重要</option>
                  <option value={2}>2 - 较不重要</option>
                  <option value={3}>3 - 一般</option>
                  <option value={4}>4 - 重要</option>
                  <option value={5}>5 - 非常重要</option>
                </select>
              </div>

              <div>
                <label htmlFor="urgency" className="block text-sm font-medium text-gray-700 mb-2">
                  紧急性 *
                </label>
                <select
                  id="urgency"
                  name="urgency"
                  required
                  value={formData.urgency}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value={1}>1 - 不紧急</option>
                  <option value={2}>2 - 较不紧急</option>
                  <option value={3}>3 - 一般</option>
                  <option value={4}>4 - 紧急</option>
                  <option value={5}>5 - 非常紧急</option>
                </select>
              </div>
            </div>

            {/* 截止时间和预估时长 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="deadline" className="block text-sm font-medium text-gray-700 mb-2">
                  截止时间 *
                </label>
                <input
                  type="datetime-local"
                  id="deadline"
                  name="deadline"
                  required
                  value={formData.deadline}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>

              <div>
                <label htmlFor="estimatedDuration" className="block text-sm font-medium text-gray-700 mb-2">
                  预估时长（分钟）*
                </label>
                <input
                  type="number"
                  id="estimatedDuration"
                  name="estimatedDuration"
                  required
                  min="5"
                  max="480"
                  step="5"
                  value={formData.estimatedDuration}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
                <p className="mt-1 text-sm text-gray-500">建议：5-480分钟</p>
              </div>
            </div>

            {/* 四象限预览 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">四象限分类预览</h3>
              <div className="text-sm text-gray-600">
                {formData.importance >= 4 && formData.urgency >= 4 && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    第一象限：重要且紧急 - 立即执行
                  </span>
                )}
                {formData.importance >= 4 && formData.urgency < 4 && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    第二象限：重要不紧急 - 计划执行
                  </span>
                )}
                {formData.importance < 4 && formData.urgency >= 4 && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    第三象限：不重要但紧急 - 委托处理
                  </span>
                )}
                {formData.importance < 4 && formData.urgency < 4 && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    第四象限：不重要不紧急 - 减少或删除
                  </span>
                )}
              </div>
            </div>

            {/* 提交按钮 */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={loading}
                className="flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                创建任务
              </button>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
}
