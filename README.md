# TimeManager - 智能时间规划助手

一款基于三大算法的智能时间管理应用，帮助用户高效规划时间，保持生活平衡。

## 🎯 核心功能

### 三大智能算法
1. **时间规划算法** - 基于四象限法则的智能任务排序
2. **生活平衡算法** - 分析工作、提升、娱乐的时间分配
3. **修复算法** - 检测并修复长期推迟的任务

### 主要特性
- 📊 智能任务分类（工作、提升、娱乐）
- 🎯 四象限自动分类（重要性 × 紧急性）
- ⏰ 自动生成今日时间安排
- 📈 生活平衡分析和建议
- 🔧 推迟任务智能修复
- ⚡ **实时时间调整**：任务超时自动重新规划
- 🕐 **用户时间收集**：个性化工作时间和生活节奏配置
- 🔄 **多策略调整**：保守、平衡、激进三种调整策略
- 📱 响应式设计，支持多设备

## 🛠️ 技术栈

- **前端**: Next.js 14 + TypeScript + Tailwind CSS
- **状态管理**: Zustand
- **数据库**: Supabase (PostgreSQL + 实时功能)
- **认证**: Supabase Auth
- **UI组件**: Lucide React + Radix UI
- **部署**: Vercel

## 🚀 快速开始

### 1. 环境要求
- Node.js 18+
- npm 或 yarn

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
复制 `.env.local` 文件并配置以下环境变量：

```bash
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_DATABASE_NAME = TimeManager;
SUPABASE_DATABASE_PASSWORD = 4kVJulB4ZRXwcuHx

# 应用配置
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=TimeManager
```

### 4. 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📁 项目结构

```
timemanager/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── auth/              # 认证页面
│   │   ├── dashboard/         # 主控制台
│   │   └── tasks/            # 任务管理
│   ├── lib/                 # 工具库
│   │   ├── algorithms/      # 三大算法
│   │   └── supabase.ts     # 数据库配置
│   ├── store/              # 状态管理
│   └── types/              # TypeScript类型
└── public/                 # 静态资源
```

## 🧮 算法说明

### 时间规划算法
- **评分公式**: `重要性 × 0.6 + 紧急性 × 0.4 + 分类权重 + 推迟惩罚`
- **四象限分类**: 自动将任务分配到合适的象限
- **时间安排**: 考虑工作时间和任务时长生成最优安排

### 生活平衡算法
- **理想比例**: 工作60% + 提升25% + 娱乐15%
- **平衡评分**: 基于偏差计算0-100分的平衡分数
- **智能建议**: 根据分析结果提供个性化建议

### 修复算法
- **推迟检测**: 识别长期被推迟的任务
- **紧急程度**: 综合推迟次数、截止时间等因素评估
- **修复建议**: 提供具体的解决方案

## 📝 开发状态

### 已完成 ✅
- [x] 项目初始化和基础架构
- [x] 用户认证系统
- [x] 三大算法实现
- [x] 基础UI界面
- [x] 任务CRUD功能
- [x] **用户时间配置收集**：工作时间、睡眠时间、固定时间段设置
- [x] **智能时间调整算法**：处理任务超时和紧急任务插入
- [x] **多策略调整系统**：保守、平衡、激进三种策略
- [x] **用户引导流程**：新用户时间配置向导

### 下一步 📋
- [ ] 应用时间调整算法到实际场景
- [ ] 日历集成功能
- [ ] 桌面悬浮窗（Tauri）
- [ ] 推送通知系统
- [ ] 时间调整历史记录和分析

## 🚀 部署

项目可以轻松部署到 Vercel：

1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 自动部署

## 📄 许可证

MIT License
