import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// 检查是否配置了有效的Supabase环境变量
const isSupabaseConfigured = supabaseUrl &&
  supabaseAnonKey &&
  supabaseUrl !== 'your_supabase_url_here' &&
  supabaseAnonKey !== 'your_supabase_anon_key_here' &&
  supabaseUrl.startsWith('https://');

let supabaseClient: any = null;

if (isSupabaseConfigured) {
  supabaseClient = createClient(supabaseUrl!, supabaseAnonKey!, {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    },
    realtime: {
      params: {
        eventsPerSecond: 10
      }
    }
  });
} else {
  console.warn('⚠️ Supabase not configured. Running in development mode without database.');
  // 创建一个模拟的Supabase客户端用于开发
  supabaseClient = {
    auth: {
      signUp: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
      signInWithPassword: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
      signOut: () => Promise.resolve({ error: null }),
      getUser: () => Promise.resolve({ data: { user: null }, error: null }),
      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } })
    },
    from: () => ({
      select: () => ({ eq: () => ({ order: () => Promise.resolve({ data: [], error: null }) }) }),
      insert: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
      update: () => Promise.resolve({ error: new Error('Supabase not configured') }),
      delete: () => Promise.resolve({ error: new Error('Supabase not configured') })
    })
  };
}

export const supabase = supabaseClient;

// 数据库类型定义
export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string;
          email: string;
          name: string | null;
          timezone: string;
          work_hours: {
            start: string;
            end: string;
            days?: string[];
          };
          category_ratios: {
            work: number;
            improvement: number;
            entertainment: number;
          };
          time_config: any; // 详细的时间配置
          onboarding_completed: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          name?: string | null;
          timezone?: string;
          work_hours?: {
            start: string;
            end: string;
            days?: string[];
          };
          category_ratios?: {
            work: number;
            improvement: number;
            entertainment: number;
          };
          time_config?: any;
          onboarding_completed?: boolean;
        };
        Update: {
          name?: string | null;
          timezone?: string;
          work_hours?: {
            start: string;
            end: string;
            days?: string[];
          };
          category_ratios?: {
            work: number;
            improvement: number;
            entertainment: number;
          };
          time_config?: any;
          onboarding_completed?: boolean;
        };
      };
      tasks: {
        Row: {
          id: string;
          user_id: string;
          title: string;
          description: string | null;
          category: 'work' | 'improvement' | 'entertainment';
          importance: number;
          urgency: number;
          deadline: string;
          estimated_duration: number;
          status: 'pending' | 'in-progress' | 'completed' | 'postponed';
          postpone_count: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          title: string;
          description?: string | null;
          category: 'work' | 'improvement' | 'entertainment';
          importance: number;
          urgency: number;
          deadline: string;
          estimated_duration: number;
          status?: 'pending' | 'in-progress' | 'completed' | 'postponed';
          postpone_count?: number;
        };
        Update: {
          title?: string;
          description?: string | null;
          category?: 'work' | 'improvement' | 'entertainment';
          importance?: number;
          urgency?: number;
          deadline?: string;
          estimated_duration?: number;
          status?: 'pending' | 'in-progress' | 'completed' | 'postponed';
          postpone_count?: number;
        };
      };
      task_completions: {
        Row: {
          id: string;
          task_id: string;
          completed_at: string;
          actual_duration: number;
          satisfaction_score: number;
        };
        Insert: {
          task_id: string;
          actual_duration: number;
          satisfaction_score: number;
        };
        Update: {
          actual_duration?: number;
          satisfaction_score?: number;
        };
      };
      daily_stats: {
        Row: {
          id: string;
          user_id: string;
          date: string;
          work_time: number;
          improvement_time: number;
          entertainment_time: number;
          tasks_completed: number;
          tasks_postponed: number;
          balance_score: number | null;
        };
        Insert: {
          user_id: string;
          date: string;
          work_time?: number;
          improvement_time?: number;
          entertainment_time?: number;
          tasks_completed?: number;
          tasks_postponed?: number;
          balance_score?: number | null;
        };
        Update: {
          work_time?: number;
          improvement_time?: number;
          entertainment_time?: number;
          tasks_completed?: number;
          tasks_postponed?: number;
          balance_score?: number | null;
        };
      };
    };
  };
}

// 认证相关工具函数
export const auth = {
  signUp: async (email: string, password: string) => {
    return await supabase.auth.signUp({ email, password });
  },
  
  signIn: async (email: string, password: string) => {
    return await supabase.auth.signInWithPassword({ email, password });
  },
  
  signOut: async () => {
    return await supabase.auth.signOut();
  },
  
  getCurrentUser: async () => {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  },
  
  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback);
  }
};
