# TimeManager 项目初始化完成 🎉

## 📋 项目状态

✅ **已完成的功能**
- [x] Next.js 14 项目初始化
- [x] TypeScript + Tailwind CSS 配置
- [x] 三大核心算法实现
  - 时间规划算法（四象限分类 + 智能排序）
  - 生活平衡算法（7天统计分析）
  - 修复算法（推迟任务检测）
- [x] 状态管理（Zustand）
- [x] 基础UI界面
  - 登录/注册页面
  - 主控制台
  - 任务创建页面
- [x] 用户认证系统架构
- [x] 数据库模型设计

## 🚀 当前可以运行

项目现在可以在开发模式下运行：

```bash
cd timemanager
npm run dev
```

访问 http://localhost:3000 查看应用界面。

⚠️ **注意**: 目前运行在开发模式，没有连接真实数据库，所有数据操作会显示"Supabase not configured"的提示。

## 🔧 下一步：配置Supabase数据库

要让应用完全功能化，需要配置Supabase数据库：

### 1. 创建Supabase项目
1. 访问 [supabase.com](https://supabase.com)
2. 创建新项目
3. 获取项目URL和API密钥

### 2. 配置环境变量
在 `timemanager/.env.local` 文件中更新：

```bash
# 替换为你的Supabase配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
```

### 3. 创建数据库表
在Supabase SQL编辑器中执行以下SQL：

```sql
-- 用户配置表
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email VARCHAR UNIQUE NOT NULL,
  name VARCHAR,
  timezone VARCHAR DEFAULT 'UTC',
  work_hours JSONB DEFAULT '{"start": "09:00", "end": "18:00"}',
  category_ratios JSONB DEFAULT '{"work": 0.6, "improvement": 0.25, "entertainment": 0.15}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 任务表
CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  description TEXT,
  category VARCHAR CHECK (category IN ('work', 'improvement', 'entertainment')),
  importance INTEGER CHECK (importance BETWEEN 1 AND 5),
  urgency INTEGER CHECK (urgency BETWEEN 1 AND 5),
  deadline TIMESTAMP,
  estimated_duration INTEGER,
  status VARCHAR DEFAULT 'pending' CHECK (status IN ('pending', 'in-progress', 'completed', 'postponed')),
  postpone_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 任务完成记录表
CREATE TABLE task_completions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
  completed_at TIMESTAMP DEFAULT NOW(),
  actual_duration INTEGER,
  satisfaction_score INTEGER CHECK (satisfaction_score BETWEEN 1 AND 5)
);

-- 每日统计表
CREATE TABLE daily_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  work_time INTEGER DEFAULT 0,
  improvement_time INTEGER DEFAULT 0,
  entertainment_time INTEGER DEFAULT 0,
  tasks_completed INTEGER DEFAULT 0,
  tasks_postponed INTEGER DEFAULT 0,
  balance_score INTEGER,
  UNIQUE(user_id, date)
);

-- 启用行级安全策略
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_completions ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_stats ENABLE ROW LEVEL SECURITY;

-- 创建安全策略
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can view own tasks" ON tasks FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own completions" ON task_completions FOR ALL USING (
  auth.uid() = (SELECT user_id FROM tasks WHERE tasks.id = task_completions.task_id)
);
CREATE POLICY "Users can view own stats" ON daily_stats FOR ALL USING (auth.uid() = user_id);
```

## 📁 项目结构说明

```
timemanager/
├── src/
│   ├── app/                    # Next.js App Router页面
│   │   ├── auth/              # 认证相关页面
│   │   ├── dashboard/         # 主控制台
│   │   └── tasks/            # 任务管理页面
│   ├── lib/                   # 工具库
│   │   ├── algorithms/        # 三大核心算法
│   │   │   ├── planning.ts   # 时间规划算法
│   │   │   ├── balance.ts    # 生活平衡算法
│   │   │   └── fix.ts        # 修复算法
│   │   └── supabase.ts       # 数据库配置
│   ├── store/                # 状态管理
│   │   ├── useAuthStore.ts   # 认证状态
│   │   └── useTaskStore.ts   # 任务状态
│   └── types/                # TypeScript类型定义
├── .env.local                # 环境变量配置
└── README.md                 # 项目说明
```

## 🎯 核心算法说明

### 1. 时间规划算法 (`src/lib/algorithms/planning.ts`)
- **评分公式**: `重要性 × 0.6 + 紧急性 × 0.4 + 分类权重 + 推迟惩罚`
- **四象限分类**: 自动将任务分配到合适的象限
- **时间安排**: 生成最优的今日时间安排

### 2. 生活平衡算法 (`src/lib/algorithms/balance.ts`)
- **理想比例**: 工作60% + 提升25% + 娱乐15%
- **平衡评分**: 基于偏差计算0-100分的平衡分数
- **智能建议**: 根据分析结果提供个性化建议

### 3. 修复算法 (`src/lib/algorithms/fix.ts`)
- **推迟检测**: 识别长期被推迟的任务
- **紧急程度**: 综合推迟次数、截止时间等因素评估
- **修复建议**: 提供具体的解决方案

## 🔄 开发流程

1. **配置Supabase** - 按照上述步骤配置数据库
2. **测试基础功能** - 注册用户、创建任务
3. **验证算法** - 测试三大算法的效果
4. **UI优化** - 根据需要调整界面
5. **功能扩展** - 添加日历集成、桌面悬浮窗等

## 📞 技术支持

如果在配置过程中遇到问题：
1. 检查环境变量是否正确配置
2. 确认Supabase项目设置
3. 查看浏览器控制台的错误信息
4. 参考项目文档和代码注释

项目已经具备了完整的架构和核心功能，配置好数据库后即可正常使用！ 🚀
